# Lokus - Product Requirements Document (PRD)

## 1. Introduction

*   **Purpose**: This document outlines the product requirements for Lokus, a platform connecting property seekers with listers. It details features for the Minimum Viable Product (MVP) and subsequent phases, aligning with the vision in `lokus_plan.md`.
*   **Vision**: To create a dynamic, user-friendly platform for listing, searching, and facilitating connections around properties.
*   **Target Audience**: Public Users (Buyers/Renters), Property Listers (Developers, Owners, Agents), Company Admin.

## 2. Goals

*   Launch an MVP with core property listing and search functionalities.
*   Provide a seamless and intuitive user experience for all user roles.
*   Ensure robust admin capabilities for platform management.
*   Iteratively add features based on user feedback and market needs, focusing on real-time capabilities and map integration.

## 3. User Roles & Personas

*   (As defined in `lokus_plan.md` - Section II)

## 4. Product Features

### 4.1. Phase 1: MVP - Core Functionality (Addressing Gaps & Current Status)

| Feature Category          | Requirement                                                                                                | Status/Notes (from code review)                                                                                                                               | Priority |
| :------------------------ | :--------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------- |
| **User Auth & Profiles**  | Secure registration (email/password)                                                                       | Implemented (Laravel Auth)                                                                                                                                    | High     |
|                           | Role selection during/after registration (Lister: Developer, Owner, Agent; Public User)                    | `RoleMiddleware` exists. Lister role assignment flow for frontend needs clear definition & implementation.                                                    | High     |
|                           | Profile management (contact info, etc.)                                                                    | `ProfileController` exists. Frontend UI for listers needed.                                                                                                   | High     |
|                           | Password recovery                                                                                          | Implemented (Laravel Auth)                                                                                                                                    | High     |
| **Property Listing**      | Lister-facing property submission form (intuitive, potentially multi-step)                                 | Partially implemented (`CreateProperty` Livewire component) with Lat/Lng inputs and Leaflet map picker. Further UI refinements may be needed. `role:lister` middleware temporarily commented out for accessibility. | High     |
|                           | Property Types: Apartments, Land (MVP focus)                                                               | `PropertyResource` (Filament) supports many types. Lister form should initially focus on these.                                                               | High     |
|                           | Basic Info: Title, Description, Price, Currency                                                            | Supported by `Property` model and `PropertyController`.                                                                                                       | High     |
|                           | **Location**: Address input, Country, City, Area, Zip Code                                                 | Supported by `Property` model.                                                                                                                                | High     |
|                           | **Location (Map Integration)**: Geocoding & pin drop on map for listers                                    | **Implemented (MVP)**: Leaflet map on Create Property page allows click-to-set Lat/Lng. Manual Lat/Lng input in Filament Admin. Geocoding (address to coords) is Phase 2. | High     |
|                           | Detailed Features (Basic for MVP types)                                                                    | `Property` model has fields. Lister form needs to present relevant fields.                                                                                    | Medium   |
|                           | Image Upload (max 15, compression/resizing)                                                                | `PropertyController` & Filament `PropertyResource` support multiple images with resizing. Lister frontend UI for `CreateProperty` Livewire component implemented. | High     |
|                           | Availability Status (Available, Rented, Sold, Draft)                                                       | Supported by `Property` model and `PropertyController`.                                                                                                       | High     |
| **Property Search**       | Keyword Search (address, landmark, ID)                                                                     | `PropertySearch` Livewire component implements keyword search (title, desc, city, state).                                                                     | High     |
|                           | Basic Filters: Property Type, For Sale/Rent, Price                                                         | `PropertySearch` Livewire component implements these.                                                                                                         | High     |
|                           | **Map-Based Search**: Interactive map display of properties as pins                                        | **Implemented (MVP)**: Leaflet map view on search results page displays markers for current page properties. Updates with Livewire.                               | High     |
| **Property Display**      | Property detail page                                                                                       | `PropertyController@show` implies existence. Frontend UI exists.                                                                                              | High     |
|                           | Image carousel                                                                                             | Backend supports multiple images. Basic Alpine.js carousel implemented on detail page.                                                                        | High     |
|                           | Detailed property information                                                                              | Data available via `Property` model, displayed on detail page.                                                                                                | High     |
|                           | **Embedded Map**: Show property location on detail page                                                    | **Implemented (MVP)**: Leaflet map on property detail page shows location if coordinates exist.                                                               | High     |
|                           | Lister's contact information/form (email-based)                                                            | `ContactController` & `PropertyInquiry` mailer exist. Contact card on detail page. Modal for contact form exists (functionality TBD).                             | High     |
| **Admin Panel**           | User Management (View, Edit, Activate/Deactivate)                                                          | Implemented (Filament `UserResource`, Livewire `Admin/UserManagement`).                                                                                       | High     |
|                           | Property Management (View, Approve/Reject, Edit, Delete)                                                   | Implemented (Filament `PropertyResource` with approve/reject actions, Lat/Lng fields added).                                                                  | High     |
| **Navigation**            | Main application navigation links                                                                          | Implemented: Header navigation links in `app.blade.php` updated to use correct Laravel routes (Home, Search, My Listings, Create Listing, Auth links). Active states basic styling added. | High     |
| **General**               | Responsive Web Design                                                                                      | Standard requirement. To be ensured during frontend development. Current structure is responsive.                                                             | High     |

### 4.2. Phase 2: Enhancements & Core Real-Time

*   (As per `lokus_plan.md` - Section V.B, to be detailed after MVP stabilization)
    *   Advanced search filters
    *   Map-based interactive search (search as map moves, draw-on-map) - *Builds on MVP map integration*
    *   Saved searches and email alerts
    *   User dashboards for listers (manage listings, view performance)
    *   In-app messaging (basic version)
    *   Real-time notifications (new messages, new matching listings)
    *   Support for all specified property types and their differentiated features in lister forms.
    *   Social sharing

### 4.3. Phase 3: Advanced Features & Scaling

*   (As per `lokus_plan.md` - Section V.C, to be detailed later)
    *   Full real-time chat
    *   Mobile apps (iOS, Android)
    *   Admin analytics and reporting
    *   Lister verification
    *   Featured listings

## 5. Non-Functional Requirements

*   **Performance**: Fast load times, responsive UI.
*   **Scalability**: Architecture to support growing users and data.
*   **Security**: Secure user data, protection against common web vulnerabilities.
*   **Usability**: Intuitive and easy-to-use interface for all roles.
*   **Reliability**: High availability and uptime.

## 6. Design & UX Considerations

*   (As per `lokus_plan.md` - Section VI)
*   Focus on clear visual hierarchy for property information.
*   Ensure map interactions are smooth and intuitive.

## 7. Open Questions & Items for Clarification

*   **Mapping Service**: Leaflet (open-source) chosen and implemented for MVP. Geocoding service (e.g., Nominatim via a third-party package or custom integration) for address-to-coordinate conversion to be considered for Phase 2.
*   **Lister Role Workflow**: Detailed workflow for lister role selection (Developer, Owner, Agent) and any associated verification process. `role:lister` middleware currently temporarily disabled for broader access during MVP development.
*   Prioritization of social login options (Google, Facebook, etc.) for Phase 2.
*   Specifics of "dynamic feature fields" based on property type for the lister-facing form.

## 8. Release Plan

*   **MVP Release**: Target completion of all "High" priority items in Section 4.1.
*   Subsequent releases will focus on Phase 2 and Phase 3 features.

## 9. Success Metrics

*   Number of registered users (seekers and listers).
*   Number of active property listings.
*   User engagement (searches performed, properties viewed, inquiries sent).
*   Task completion rates (e.g., successful property submission).
---

This PRD outline forms a basis for development. Core MVP map integration features (manual coordinate input, map-based coordinate selection for creation, map display on detail/search pages) and navigation link fixes have been implemented. The `role:lister` middleware is temporarily commented out for easier testing of lister-specific routes like property creation.
