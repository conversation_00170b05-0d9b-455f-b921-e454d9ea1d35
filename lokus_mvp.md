Product Requirements Document: Lokus - MVP
1. Introduction
This document outlines the requirements for the Minimum Viable Product (MVP) of Lokus, a web-based real estate platform. The MVP will focus on enabling users to list properties, search for properties, view property details, and contact listers. An admin panel will provide basic oversight. The platform will be built using the Laravel PHP framework.
2. Goals of the MVP
Validate the core concept of a user-driven property listing platform.
Enable property owners/agents (Listers) to easily list properties.
Allow public users (Seekers) to effectively search and find properties.
Provide a basic administrative interface for platform management.
Gather user feedback for future iterations.
Establish a foundational Laravel codebase for future expansion.
3. Target Audience (MVP Focus)
Public Users (Seekers): Individuals looking to rent or buy properties.
Property Listers (Owners/Agents): Individuals or small agencies wanting to list properties for sale or rent. (For <PERSON>, we'll combine <PERSON><PERSON><PERSON>, Owner, Agent into a single "Lister" role with common listing abilities).
Platform Admin: Company staff responsible for overseeing the platform.
4. User Roles & Permissions (MVP)
Unregistered User (Seeker - Guest):
Search/browse properties.
View property details.
Contact lister via a form (no direct messaging in MVP).
Registered User (Lister):
All Seeker permissions.
Register and log in.
Submit new property listings.
Manage (view, edit, delete, mark as sold/rented) their own listings.
View inquiries received for their properties (via email notifications).
Admin:
Log in to a separate admin area.
Manage users (view list, potentially activate/deactivate).
Manage all property listings (view, approve/reject, edit, delete).
5. Proposed Features (MVP Scope)
5.1. User Authentication (Leveraging Laravel Breeze/Jetstream)
U-AUTH-01: User Registration (Email, Password, Name, Role Selection: "I want to list properties" or "I'm just looking" - latter becomes a standard user, former becomes a Lister).
U-AUTH-02: User Login.
U-AUTH-03: Password Reset.
U-AUTH-04: User Logout.
U-AUTH-05: Basic Profile page for Listers (Name, Contact Email, Phone - visible on their listings).
5.2. Property Listing Submission & Management (for Listers)
P-LIST-01: Create New Property Listing Form:
Property Type (Dropdown): Apartment, House/Bungalow, Land, Single Room (MVP focus).
Listing Type (Radio/Dropdown): For Sale, For Rent.
Title: Text input.
Description: Text area.
Price: Number input, Currency (default set by admin, not selectable per listing in MVP).
Location:
Address Line 1, City, State/Region, Zip Code (Text inputs).
MVP Map Simplification: No interactive map for pin dropping during submission. Admin can add coordinates later if needed, or we rely on geocoding from address for display.
Basic Features (Conditional on Property Type - simplified for MVP):
Apartment/House/Bungalow/Single Room: Bedrooms (Number), Bathrooms (Number), Square Footage (Number).
Land: Plot Size (Number).
Image Upload:
Up to 15 images.
Basic file selector.
Server-side image resizing/optimization (e.g., using Intervention Image).
Status: Draft, Published (default to Published if submitted, or requires admin approval).
P-LIST-02: View My Listings: A dashboard page for Listers showing their submitted properties.
P-LIST-03: Edit My Listing: Ability to modify details of their own existing listings.
P-LIST-04: Delete My Listing: Ability to remove their own listings.
P-LIST-05: Mark Listing as Sold/Rented: Changes status, potentially hides from active search.
5.3. Property Search & Display (for Seekers & Listers)
P-SRCH-01: Basic Search Bar (Homepage/Header): Search by keywords (e.g., city, area, title).
P-SRCH-02: Search Results Page:
List view of properties matching criteria.
Basic Filters: Property Type, For Sale/For Rent, Price Range (simple input).
Pagination for results.
P-SRCH-03: Property Detail Page:
Display all submitted property information.
Image Carousel: For viewing uploaded images (using a simple JS library).
Lister's contact info (Name, Email, Phone - if provided).
Contact Lister Form: Sends an email to the Lister (using Laravel's mail capabilities) with Seeker's name, email, message.
MVP Map Simplification: Static map image (e.g., OpenStreetMap or Google Maps Static API if budget allows) showing property location based on address/coordinates. No interactive map on this page for MVP.
5.4. Admin Panel (Basic - can be built with standard Laravel views/controllers or a simple admin panel generator)
ADM-USER-01: View User List: Table of all registered users with basic info (Name, Email, Role, Registration Date).
ADM-USER-02: (Optional MVP) Activate/Deactivate User Accounts.
ADM-PROP-01: View All Property Listings: Table of all properties with key details and status.
ADM-PROP-02: Approve/Reject New Listings: If listings require moderation.
ADM-PROP-03: Edit Any Property Listing.
ADM-PROP-04: Delete Any Property Listing.
ADM-SET-01: (Optional MVP) Basic site settings (e.g., default currency symbol).
5.5. Real-Time Features (MVP - Minimal / Deferred)
RT-MVP-01: Email Notifications:
To Lister: New inquiry received via contact form.
To Admin: New Lister registration (optional).
To Lister: Listing approved/rejected (if moderation is on).
Out of Scope for MVP: Real-time chat, live map updates, WebSocket-based notifications.
6. Non-Functional Requirements (MVP)
NFR-01: Performance: Pages should load within a reasonable time (e.g., < 3-5 seconds) on a standard internet connection. Basic query optimization.
NFR-02: Usability: Clean, intuitive interface. Easy to navigate.
NFR-03: Security: Basic Laravel security best practices (CSRF protection, XSS prevention, SQL injection prevention via Eloquent). Secure password hashing.
NFR-04: Responsiveness: The website should be usable on common desktop and mobile browser sizes.
NFR-05: Browser Compatibility: Support latest versions of major browsers (Chrome, Firefox, Safari, Edge).
NFR-06: Data Integrity: Use Laravel's validation for all form submissions.
7. Technology Stack (Laravel Focus)
Backend: Laravel (latest stable version)
Frontend: Blade templates with HTML, CSS, JavaScript (e.g., Alpine.js or minimal vanilla JS for interactivity like image carousel).
Database: MySQL or PostgreSQL (as per Laravel standard).
Web Server: Apache or Nginx.
Image Handling: Intervention Image library for resizing/optimization.
File Storage: Laravel Filesystem (local for dev, S3-compatible for prod if budget allows).
Mapping (Display Only): Leaflet.js with OpenStreetMap (free) for static map pin on property page, or Google Maps Static API.
Version Control: Git.
8. Success Metrics for MVP
Number of registered Listers.
Number of successfully submitted and published properties.
Number of property views.
Number of inquiries sent via contact forms.
Average time spent on site.
Qualitative feedback from early users.
9. Future Considerations (Out of MVP Scope)
Advanced search filters and map-based interactive search.
Differentiated lister roles (Developer, Owner, Agent) with specific features.
Real-time chat and notifications (WebSockets).
User dashboards with analytics for listers.
Saved searches and email alerts for seekers.
Native mobile applications.
Payment integrations for featured listings or subscriptions.
Social logins.
Admin analytics and reporting.
Lister verification.
10. Release Criteria for MVP
All defined MVP features are implemented and tested.
Major bugs and critical usability issues are resolved.
Basic security checks passed.
Platform is deployable to a staging/production environment.