{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "axios": "^1.7.4", "laravel-vite-plugin": "^1.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.8", "alpinejs": "^3.14.9", "autoprefixer": "^10.4.20", "concurrently": "^9.0.1", "postcss": "^8.4.31", "tailwindcss": "^4.1.8", "vite": "^6.0"}}