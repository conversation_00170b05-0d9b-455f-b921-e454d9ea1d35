<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lokus - Modern Real Estate Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.13.3/cdn.min.js" defer></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        [x-cloak] { display: none !important; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-4px); box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
        .fade-in { animation: fadeIn 0.6s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
    </style>
</head>
<body class="bg-gray-50" x-data="{ 
    currentPage: 'home', 
    showMobileMenu: false,
    showLoginModal: false,
    showContactModal: false,
    searchQuery: '',
    propertyType: 'all',
    listingType: 'all',
    priceRange: [0, 1000000],
    viewMode: 'grid',
    properties: [
        {
            id: 1,
            title: 'Modern Downtown Apartment',
            type: 'Apartment',
            listingType: 'For Rent',
            price: 2850,
            location: 'Downtown, NYC',
            bedrooms: 2,
            bathrooms: 2,
            sqft: 1200,
            image: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',
            images: [
                'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800&h=600&fit=crop'
            ],
            lister: 'Sarah Johnson',
            phone: '+****************',
            email: '<EMAIL>',
            description: 'Beautiful modern apartment in the heart of downtown with stunning city views.'
        },
        {
            id: 2,
            title: 'Luxury Family House',
            type: 'House',
            listingType: 'For Sale',
            price: 750000,
            location: 'Westfield, CA',
            bedrooms: 4,
            bathrooms: 3,
            sqft: 2800,
            image: 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',
            images: [
                'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1505843513577-22bb7d21e455?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1494526585095-c41746248156?w=800&h=600&fit=crop'
            ],
            lister: 'Mike Chen',
            phone: '+****************',
            email: '<EMAIL>',
            description: 'Spacious family home with modern amenities and a beautiful garden.'
        },
        {
            id: 3,
            title: 'Cozy Studio Apartment',
            type: 'Single Room',
            listingType: 'For Rent',
            price: 1200,
            location: 'Brooklyn, NYC',
            bedrooms: 0,
            bathrooms: 1,
            sqft: 450,
            image: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop',
            images: [
                'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop'
            ],
            lister: 'Emma Davis',
            phone: '+****************',
            email: '<EMAIL>',
            description: 'Perfect studio for young professionals in trendy Brooklyn neighborhood.'
        }
    ],
    selectedProperty: null,
    currentImageIndex: 0
}">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center cursor-pointer" @click="currentPage = 'home'">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Lokus
                        </h1>
                    </div>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" @click="currentPage = 'home'" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" :class="currentPage === 'home' ? 'text-blue-600' : ''">Home</a>
                    <a href="#" @click="currentPage = 'search'" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" :class="currentPage === 'search' ? 'text-blue-600' : ''">Search</a>
                    <a href="#" @click="currentPage = 'dashboard'" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors" :class="currentPage === 'dashboard' ? 'text-blue-600' : ''">My Listings</a>
                    <button @click="showLoginModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Sign In
                    </button>
                </div>
                
                <div class="md:hidden flex items-center">
                    <button @click="showMobileMenu = !showMobileMenu" class="text-gray-700">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div x-show="showMobileMenu" x-cloak class="md:hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#" @click="currentPage = 'home'; showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600">Home</a>
                <a href="#" @click="currentPage = 'search'; showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600">Search</a>
                <a href="#" @click="currentPage = 'dashboard'; showMobileMenu = false" class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600">My Listings</a>
                <button @click="showLoginModal = true; showMobileMenu = false" class="block w-full text-left px-3 py-2 text-base font-medium text-blue-600">Sign In</button>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div x-show="currentPage === 'home'" x-cloak class="fade-in">
        <!-- Hero Section -->
        <section class="gradient-bg text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                <div class="text-center">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Find Your Perfect
                        <span class="block">Dream Home</span>
                    </h1>
                    <p class="text-xl md:text-2xl mb-12 text-blue-100">
                        Discover amazing properties with our modern, intuitive platform
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="max-w-4xl mx-auto bg-white p-6 rounded-2xl shadow-2xl">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="md:col-span-2">
                                <input 
                                    type="text" 
                                    placeholder="Search by location, property type..."
                                    x-model="searchQuery"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                                >
                            </div>
                            <select x-model="propertyType" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900">
                                <option value="all">All Types</option>
                                <option value="apartment">Apartment</option>
                                <option value="house">House</option>
                                <option value="land">Land</option>
                                <option value="single-room">Single Room</option>
                            </select>
                            <button @click="currentPage = 'search'" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                <i class="fas fa-search mr-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Properties -->
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Properties</h2>
                    <p class="text-xl text-gray-600">Discover our handpicked selection of premium properties</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <template x-for="property in properties" :key="property.id">
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-hover cursor-pointer" @click="selectedProperty = property; currentPage = 'property'">
                            <div class="relative">
                                <img :src="property.image" :alt="property.title" class="w-full h-64 object-cover">
                                <div class="absolute top-4 left-4">
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium" x-text="property.listingType"></span>
                                </div>
                                <div class="absolute top-4 right-4">
                                    <button class="bg-white/80 hover:bg-white text-gray-700 p-2 rounded-full transition-colors">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2" x-text="property.title"></h3>
                                <p class="text-gray-600 mb-3" x-text="property.location"></p>
                                <div class="flex items-center justify-between mb-4">
                                    <div class="text-2xl font-bold text-blue-600">
                                        $<span x-text="property.price.toLocaleString()"></span>
                                        <span class="text-sm text-gray-500" x-text="property.listingType === 'For Rent' ? '/month' : ''"></span>
                                    </div>
                                </div>
                                <div class="flex items-center text-sm text-gray-600 space-x-4">
                                    <div class="flex items-center" x-show="property.bedrooms > 0">
                                        <i class="fas fa-bed mr-1"></i>
                                        <span x-text="property.bedrooms"></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-bath mr-1"></i>
                                        <span x-text="property.bathrooms"></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-ruler-combined mr-1"></i>
                                        <span x-text="property.sqft"></span> sqft
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-16 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Ready to List Your Property?</h2>
                <p class="text-xl text-gray-600 mb-8">Join thousands of property owners and agents using Lokus</p>
                <button @click="currentPage = 'create-listing'" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors">
                    Start Listing Today
                </button>
            </div>
        </section>
    </div>

    <!-- Search Page -->
    <div x-show="currentPage === 'search'" x-cloak class="fade-in py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Search Header -->
            <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div class="md:col-span-2">
                        <input 
                            type="text" 
                            placeholder="Search by location..."
                            x-model="searchQuery"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                    </div>
                    <select x-model="propertyType" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all">All Types</option>
                        <option value="apartment">Apartment</option>
                        <option value="house">House</option>
                        <option value="land">Land</option>
                        <option value="single-room">Single Room</option>
                    </select>
                    <select x-model="listingType" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all">For Sale & Rent</option>
                        <option value="sale">For Sale</option>
                        <option value="rent">For Rent</option>
                    </select>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-search mr-2"></i>
                        Search
                    </button>
                </div>
            </div>

            <!-- Results Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Search Results</h1>
                    <p class="text-gray-600"><span x-text="properties.length"></span> properties found</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button @click="viewMode = 'grid'" class="px-3 py-2 rounded-md text-sm font-medium transition-colors" :class="viewMode === 'grid' ? 'bg-white text-gray-900 shadow' : 'text-gray-600'">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button @click="viewMode = 'list'" class="px-3 py-2 rounded-md text-sm font-medium transition-colors" :class="viewMode === 'list' ? 'bg-white text-gray-900 shadow' : 'text-gray-600'">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Grid View -->
            <div x-show="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template x-for="property in properties" :key="property.id">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover cursor-pointer" @click="selectedProperty = property; currentPage = 'property'">
                        <div class="relative">
                            <img :src="property.image" :alt="property.title" class="w-full h-48 object-cover">
                            <div class="absolute top-3 left-3">
                                <span class="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium" x-text="property.listingType"></span>
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-bold text-gray-900 mb-1" x-text="property.title"></h3>
                            <p class="text-gray-600 text-sm mb-2" x-text="property.location"></p>
                            <div class="text-xl font-bold text-blue-600 mb-3">
                                $<span x-text="property.price.toLocaleString()"></span>
                                <span class="text-sm text-gray-500" x-text="property.listingType === 'For Rent' ? '/month' : ''"></span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 space-x-3">
                                <div class="flex items-center" x-show="property.bedrooms > 0">
                                    <i class="fas fa-bed mr-1"></i>
                                    <span x-text="property.bedrooms"></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-bath mr-1"></i>
                                    <span x-text="property.bathrooms"></span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-ruler-combined mr-1"></i>
                                    <span x-text="property.sqft"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- List View -->
            <div x-show="viewMode === 'list'" x-cloak class="space-y-6">
                <template x-for="property in properties" :key="property.id">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover cursor-pointer" @click="selectedProperty = property; currentPage = 'property'">
                        <div class="md:flex">
                            <div class="md:w-1/3">
                                <img :src="property.image" :alt="property.title" class="w-full h-48 md:h-full object-cover">
                            </div>
                            <div class="md:w-2/3 p-6">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-bold text-gray-900" x-text="property.title"></h3>
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium" x-text="property.listingType"></span>
                                </div>
                                <p class="text-gray-600 mb-3" x-text="property.location"></p>
                                <p class="text-gray-700 mb-4" x-text="property.description"></p>
                                <div class="flex justify-between items-center">
                                    <div class="text-2xl font-bold text-blue-600">
                                        $<span x-text="property.price.toLocaleString()"></span>
                                        <span class="text-sm text-gray-500" x-text="property.listingType === 'For Rent' ? '/month' : ''"></span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-600 space-x-4">
                                        <div class="flex items-center" x-show="property.bedrooms > 0">
                                            <i class="fas fa-bed mr-1"></i>
                                            <span x-text="property.bedrooms"></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-bath mr-1"></i>
                                            <span x-text="property.bathrooms"></span>
                                        </div>
                                        <div class="flex items-center">
                                            <i class="fas fa-ruler-combined mr-1"></i>
                                            <span x-text="property.sqft"></span> sqft
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Property Detail Page -->
    <div x-show="currentPage === 'property' && selectedProperty" x-cloak class="fade-in py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <button @click="currentPage = 'search'" class="mb-6 text-blue-600 hover:text-blue-700 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Search
            </button>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Image Gallery -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                        <div class="relative">
                            <img 
                                :src="selectedProperty?.images[currentImageIndex] || selectedProperty?.image" 
                                :alt="selectedProperty?.title"
                                class="w-full h-96 object-cover"
                            >
                            <div class="absolute inset-0 flex items-center justify-between p-4" x-show="selectedProperty?.images?.length > 1">
                                <button @click="currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : selectedProperty.images.length - 1" class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button @click="currentImageIndex = currentImageIndex < selectedProperty.images.length - 1 ? currentImageIndex + 1 : 0" class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-4" x-show="selectedProperty?.images?.length > 1">
                            <div class="flex space-x-2 overflow-x-auto">
                                <template x-for="(image, index) in selectedProperty?.images" :key="index">
                                    <img 
                                        :src="image" 
                                        class="w-20 h-16 object-cover rounded-lg cursor-pointer opacity-60 hover:opacity-100 transition-opacity"
                                        :class="currentImageIndex === index ? 'opacity-100 ring-2 ring-blue-500' : ''"
                                        @click="currentImageIndex = index"
                                    >
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Property Details -->
                    <div class="bg-white rounded-2xl shadow-lg p-8">
                        <div class="flex justify-between items-start mb-6">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900 mb-2" x-text="selectedProperty?.title"></h1>
                                <p class="text-xl text-gray-600" x-text="selectedProperty?.location"></p>
                            </div>
                            <span class="bg-blue-600 text-white px-4 py-2 rounded-full font-medium" x-text="selectedProperty?.listingType"></span>
                        </div>

                        <div class="text-3xl font-bold text-blue-600 mb-6">
                            $<span x-text="selectedProperty?.price.toLocaleString()"></span>
                            <span class="text-lg text-gray-500" x-text="selectedProperty?.listingType === 'For Rent' ? '/month' : ''"></span>
                        </div>

                        <div class="grid grid-cols-3 gap-6 mb-8">
                            <div class="text-center p-4 bg-gray-50 rounded-lg" x-show="selectedProperty?.bedrooms > 0">
                                <i class="fas fa-bed text-2xl text-blue-600 mb-2"></i>
                                <div class="text-2xl font-bold text-gray-900" x-text="selectedProperty?.bedrooms"></div>
                                <div class="text-sm text-gray-600">Bedrooms</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <i class="fas fa-bath text-2xl text-blue-600 mb-2"></i>
                                <div class="text-2xl font-bold text-gray-900" x-text="selectedProperty?.bathrooms"></div>
                                <div class="text-sm text-gray-600">Bathrooms</div>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <i class="fas fa-ruler-combined text-2xl text-blue-600 mb-2"></i>
                                <div class="text-2xl font-bold text-gray-900" x-text="selectedProperty?.sqft"></div>
                                <div class="text-sm text-gray-600">Sq Ft</div>
                            </div>
                        </div>

                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Description</h3>
                            <p class="text-gray-700 leading-relaxed" x-text="selectedProperty?.description"></p>
                        </div>

                        <!-- Map Placeholder -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Location</h3>
                            <div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
                                <div class="text-center text-gray-600">
                                    <i class="fas fa-map-marker-alt text-3xl mb-2"></i>
                                    <p>Interactive Map</p>
                                    <p class="text-sm" x-text="selectedProperty?.location"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Contact Card -->
                    <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Lister</h3>
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                                <span x-text="selectedProperty?.lister?.charAt(0)"></span>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900" x-text="selectedProperty?.lister"></div>
                                <div class="text-sm text-gray-600">Property Lister</div>
                            </div>
                        </div>
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-phone w-5"></i>
                                <span x-text="selectedProperty?.phone"></span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-envelope w-5"></i>
                                <span x-text="selectedProperty?.email"></span>
                            </div>
                        </div>
                        <button @click="showContactModal = true" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                            <i class="fas fa-envelope mr-2"></i>
                            Send Message
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-heart mr-2"></i>
                                Save Property
                            </button>
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-share mr-2"></i>
                                Share Property
                            </button>
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-calculator mr-2"></i>
                                Mortgage Calculator
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Page -->
    <div x-show="currentPage === 'dashboard'" x-cloak class="fade-in py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">My Listings</h1>
                    <p class="text-gray-600">Manage your property listings</p>
                </div>
                <button @click="currentPage = 'create-listing'" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Listing
                </button>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-full">
                            <i class="fas fa-home text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Listings</p>
                            <p class="text-2xl font-bold text-gray-900">12</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-full">
                            <i class="fas fa-eye text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Total Views</p>
                            <p class="text-2xl font-bold text-gray-900">1,234</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 rounded-full">
                            <i class="fas fa-envelope text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Inquiries</p>
                            <p class="text-2xl font-bold text-gray-900">28</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-full">
                            <i class="fas fa-check-circle text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">Sold/Rented</p>
                            <p class="text-2xl font-bold text-gray-900">5</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listings Table -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Recent Listings</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="property in properties" :key="property.id">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <img :src="property.image" :alt="property.title" class="w-12 h-12 rounded-lg object-cover mr-4">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900" x-text="property.title"></div>
                                                <div class="text-sm text-gray-500" x-text="property.location"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="property.type"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        $<span x-text="property.price.toLocaleString()"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Published
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span x-text="Math.floor(Math.random() * 100) + 50"></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">Edit</button>
                                        <button class="text-red-600 hover:text-red-900">Delete</button>
                                        <button class="text-gray-600 hover:text-gray-900">Mark Sold</button>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Listing Page -->
    <div x-show="currentPage === 'create-listing'" x-cloak class="fade-in py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Create New Listing</h1>
                <p class="text-gray-600">Add your property to reach thousands of potential buyers and renters</p>
            </div>

            <div class="bg-white rounded-2xl shadow-lg p-8">
                <form class="space-y-8">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Basic Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Property Title</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="e.g., Modern Downtown Apartment">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
                                <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option>Select Property Type</option>
                                    <option>Apartment</option>
                                    <option>House/Bungalow</option>
                                    <option>Land</option>
                                    <option>Single Room</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Listing Type</label>
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="listing_type" value="sale" class="text-blue-600 mr-2">
                                        For Sale
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="listing_type" value="rent" class="text-blue-600 mr-2">
                                        For Rent
                                    </label>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Price ($)</label>
                                <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0">
                            </div>
                        </div>
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Describe your property..."></textarea>
                        </div>
                    </div>

                    <!-- Location -->
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Location</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Street address">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="City">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">State/Region</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="State or Region">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Zip Code</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Zip Code">
                            </div>
                        </div>
                    </div>

                    <!-- Property Features -->
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Property Features</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bedrooms</label>
                                <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Bathrooms</label>
                                <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Square Footage</label>
                                <input type="number" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Image Upload -->
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Property Images</h3>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg text-gray-600 mb-2">Drag and drop your images here</p>
                            <p class="text-sm text-gray-500 mb-4">or click to browse (Max 15 images)</p>
                            <button type="button" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Choose Files
                            </button>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-between pt-6">
                        <button type="button" @click="currentPage = 'dashboard'" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors">
                            Cancel
                        </button>
                        <div class="space-x-4">
                            <button type="button" class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors">
                                Save as Draft
                            </button>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Publish Listing
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div x-show="showLoginModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showLoginModal = false"></div>
            
            <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-8 pt-8 pb-6">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900">Welcome Back</h3>
                        <p class="text-gray-600">Sign in to your account</p>
                    </div>
                    
                    <form class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="••••••••">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="flex items-center">
                                <input type="checkbox" class="text-blue-600 mr-2">
                                <span class="text-sm text-gray-600">Remember me</span>
                            </label>
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-700">Forgot password?</a>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                            Sign In
                        </button>
                    </form>
                    
                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-600">
                            Don't have an account? 
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Sign up</a>
                        </p>
                    </div>
                </div>
                
                <div class="bg-gray-50 px-8 py-4">
                    <button @click="showLoginModal = false" class="w-full text-gray-600 hover:text-gray-700 font-medium">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Modal -->
    <div x-show="showContactModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showContactModal = false"></div>
            
            <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-8 pt-8 pb-6">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900">Contact Lister</h3>
                        <p class="text-gray-600">Send a message about this property</p>
                    </div>
                    
                    <form class="space-y-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="John">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Doe">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                            <input type="tel" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="+****************">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="I'm interested in this property..."></textarea>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                            Send Message
                        </button>
                    </form>
                </div>
                
                <div class="bg-gray-50 px-8 py-4">
                    <button @click="showContactModal = false" class="w-full text-gray-600 hover:text-gray-700 font-medium">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>