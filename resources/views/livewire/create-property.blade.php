<div class="max-w-4xl mx-auto py-10">
    <h2 class="text-2xl font-semibold text-gray-800 mb-6">Create New Property Listing</h2>

    @if (session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif

    <form wire:submit="store" class="bg-white shadow-md rounded-lg p-8 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Property Type -->
            <div>
                <x-input-label for="property_type" :value="__('Property Type')" />
                <select wire:model.live="property_type" id="property_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    <option value="">Select Property Type</option>
                    <option value="apartment">Apartment</option>
                    <option value="house">House/Bungalow</option>
                    <option value="land">Land</option>
                    <option value="single_room">Single Room</option>
                </select>
                <x-input-error :messages="$errors->get('property_type')" class="mt-2" />
            </div>

            <!-- Listing Type -->
            <div>
                <x-input-label for="listing_type" :value="__('Listing Type')" />
                <div class="mt-1 flex space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio" wire:model.live="listing_type" value="for_sale" class="form-radio">
                        <span class="ml-2 text-gray-700">For Sale</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" wire:model.live="listing_type" value="for_rent" class="form-radio">
                        <span class="ml-2 text-gray-700">For Rent</span>
                    </label>
                </div>
                <x-input-error :messages="$errors->get('listing_type')" class="mt-2" />
            </div>
        </div>

        <!-- Basic Info -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <x-input-label for="title" :value="__('Title')" />
                    <x-text-input wire:model.live="title" id="title" type="text" class="mt-1 block w-full" placeholder="e.g., Spacious 3-Bedroom Apartment" />
                    <x-input-error :messages="$errors->get('title')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="description" :value="__('Description')" />
                    <textarea wire:model.live="description" id="description" rows="5" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" placeholder="Describe your property..."></textarea>
                    <x-input-error :messages="$errors->get('description')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="price" :value="__('Price')" />
                    <x-text-input wire:model.live="price" id="price" type="number" step="0.01" class="mt-1 block w-full" placeholder="e.g., 250000" />
                    <x-input-error :messages="$errors->get('price')" class="mt-2" />
                </div>
            </div>
        </div>

        <!-- Location -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Location Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <x-input-label for="address_line_1" :value="__('Address Line 1')" />
                    <x-text-input wire:model.live="address_line_1" id="address_line_1" type="text" class="mt-1 block w-full" placeholder="e.g., 123 Main St" />
                    <x-input-error :messages="$errors->get('address_line_1')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="city" :value="__('City')" />
                    <x-text-input wire:model.live="city" id="city" type="text" class="mt-1 block w-full" placeholder="e.g., New York" />
                    <x-input-error :messages="$errors->get('city')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="state_region" :value="__('State/Region')" />
                    <x-text-input wire:model.live="state_region" id="state_region" type="text" class="mt-1 block w-full" placeholder="e.g., NY" />
                    <x-input-error :messages="$errors->get('state_region')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="zip_code" :value="__('Zip Code')" />
                    <x-text-input wire:model.live="zip_code" id="zip_code" type="text" class="mt-1 block w-full" placeholder="e.g., 10001" />
                    <x-input-error :messages="$errors->get('zip_code')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="latitude" :value="__('Latitude')" />
                    <x-text-input wire:model.live="latitude" id="latitude" type="number" step="any" class="mt-1 block w-full" placeholder="e.g., 34.052235" />
                    <x-input-error :messages="$errors->get('latitude')" class="mt-2" />
                </div>
                <div>
                    <x-input-label for="longitude" :value="__('Longitude')" />
                    <x-text-input wire:model.live="longitude" id="longitude" type="number" step="any" class="mt-1 block w-full" placeholder="e.g., -118.243683" />
                    <x-input-error :messages="$errors->get('longitude')" class="mt-2" />
                </div>
            </div>
            <div class="mt-4">
                <x-input-label :value="__('Set Location on Map')" />
                <div id="mapCreate" style="height: 300px;" class="mt-1 rounded-md border border-gray-300 shadow-sm"></div>
                <p class="mt-1 text-sm text-gray-500">Click on the map to set latitude and longitude.</p>
            </div>
        </div>

        <!-- Dynamic Features -->
        @if (in_array($property_type, ['apartment', 'house', 'single_room']))
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Property Features</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <x-input-label for="bedrooms" :value="__('Bedrooms')" />
                        <x-text-input wire:model.live="bedrooms" id="bedrooms" type="number" class="mt-1 block w-full" placeholder="e.g., 3" />
                        <x-input-error :messages="$errors->get('bedrooms')" class="mt-2" />
                    </div>
                    <div>
                        <x-input-label for="bathrooms" :value="__('Bathrooms')" />
                        <x-text-input wire:model.live="bathrooms" id="bathrooms" type="number" class="mt-1 block w-full" placeholder="e.g., 2" />
                        <x-input-error :messages="$errors->get('bathrooms')" class="mt-2" />
                    </div>
                    <div>
                        <x-input-label for="square_footage" :value="__('Square Footage')" />
                        <x-text-input wire:model.live="square_footage" id="square_footage" type="number" step="0.01" class="mt-1 block w-full" placeholder="e.g., 1500" />
                        <x-input-error :messages="$errors->get('square_footage')" class="mt-2" />
                    </div>
                </div>
            </div>
        @elseif ($property_type === 'land')
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Land Features</h3>
                <div>
                    <x-input-label for="plot_size" :value="__('Plot Size (sq ft)')" />
                    <x-text-input wire:model.live="plot_size" id="plot_size" type="number" step="0.01" class="mt-1 block w-full" placeholder="e.g., 10000" />
                    <x-input-error :messages="$errors->get('plot_size')" class="mt-2" />
                </div>
            </div>
        @endif

        <!-- Image Upload -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Property Images (Max 15)</h3>
            <input type="file" wire:model.live="images" multiple class="block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-full file:border-0
                file:text-sm file:font-semibold
                file:bg-indigo-50 file:text-indigo-700
                hover:file:bg-indigo-100"
            >
            <div wire:loading wire:target="images" class="mt-2 text-sm text-gray-500">Uploading images...</div>
            <x-input-error :messages="$errors->get('images.*')" class="mt-2" />
            <x-input-error :messages="$errors->get('images')" class="mt-2" />

            <div class="mt-4 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                @foreach ($images as $image)
                    @if (is_object($image) && method_exists($image, 'temporaryUrl'))
                        <img src="{{ $image->temporaryUrl() }}" class="w-full h-32 object-cover rounded-lg shadow-md" alt="Property Image Preview">
                    @endif
                @endforeach
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-end">
            <x-primary-button wire:loading.attr="disabled">
                {{ __('Create Listing') }}
            </x-primary-button>
        </div>
    </form>
</div>

@push('scripts')
<script>
document.addEventListener('livewire:navigated', () => {
    if (document.getElementById('mapCreate')) {
        let map = L.map('mapCreate').setView([0, 0], 2); // Default view
        let marker;

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Function to update Livewire properties and marker
        function updateLocation(lat, lng) {
            $wire.set('latitude', lat.toFixed(7));
            $wire.set('longitude', lng.toFixed(7));

            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker([lat, lng]).addTo(map);
            map.panTo([lat, lng]);
        }

        // Handle map click
        map.on('click', function(e) {
            updateLocation(e.latlng.lat, e.latlng.lng);
        });

        // If initial lat/lng are set, update map
        let initialLat = $wire.get('latitude');
        let initialLng = $wire.get('longitude');

        if (initialLat && initialLng && !isNaN(parseFloat(initialLat)) && !isNaN(parseFloat(initialLng))) {
            const lat = parseFloat(initialLat);
            const lng = parseFloat(initialLng);
            updateLocation(lat, lng);
            map.setView([lat, lng], 13); // Zoom in if coordinates are present
        } else {
            // Attempt to geolocate user for better default view, or use a default
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    map.setView([position.coords.latitude, position.coords.longitude], 10);
                }, function() {
                    // Geolocation failed or denied, use a wide default
                    map.setView([20, 0], 2); 
                });
            }
        }

        // Listen for changes from Livewire to update marker if lat/lng are changed manually
        Livewire.hook('element.updated', (el, component) => {
            if (component.name === 'create-property' && (el.getAttribute('wire:model.live') === 'latitude' || el.getAttribute('wire:model.live') === 'longitude')) {
                let currentLat = $wire.get('latitude');
                let currentLng = $wire.get('longitude');
                if (currentLat && currentLng && !isNaN(parseFloat(currentLat)) && !isNaN(parseFloat(currentLng))) {
                    const lat = parseFloat(currentLat);
                    const lng = parseFloat(currentLng);
                    if (marker) {
                        map.removeLayer(marker);
                    }
                    marker = L.marker([lat, lng]).addTo(map);
                    // Do not pan here to avoid interrupting manual input focus
                }
            }
        });
    }
});
</script>
@endpush
