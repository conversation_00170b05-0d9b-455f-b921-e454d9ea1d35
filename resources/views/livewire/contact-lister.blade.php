<div>
    <!-- Contact Lister Button -->
    <x-primary-button wire:click="$set('showModal', true)">
        Contact Lister
    </x-primary-button>

    <!-- Modal -->
    <x-modal wire:model.live="showModal">
        <form wire:submit="sendInquiry" class="p-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Contact {{ $property->user->name }}</h2>
            <p class="text-gray-600 mb-6">Inquiring about: <span class="font-medium">{{ $property->title }}</span></p>

            @if (session()->has('inquiry_message'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('inquiry_message') }}</span>
                </div>
            @endif

            <div class="mb-4">
                <x-input-label for="name" :value="__('Your Name')" />
                <x-text-input wire:model.live="name" id="name" type="text" class="mt-1 block w-full" required />
                <x-input-error :messages="$errors->get('name')" class="mt-2" />
            </div>

            <div class="mb-4">
                <x-input-label for="email" :value="__('Your Email')" />
                <x-text-input wire:model.live="email" id="email" type="email" class="mt-1 block w-full" required />
                <x-input-error :messages="$errors->get('email')" class="mt-2" />
            </div>

            <div class="mb-4">
                <x-input-label for="phone" :value="__('Your Phone (Optional)')" />
                <x-text-input wire:model.live="phone" id="phone" type="text" class="mt-1 block w-full" />
                <x-input-error :messages="$errors->get('phone')" class="mt-2" />
            </div>

            <div class="mb-6">
                <x-input-label for="message" :value="__('Your Message')" />
                <textarea wire:model.live="message" id="message" rows="5" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required></textarea>
                <x-input-error :messages="$errors->get('message')" class="mt-2" />
            </div>

            <div class="flex justify-end gap-4">
                <x-secondary-button wire:click="$set('showModal', false)" type="button">
                    Cancel
                </x-secondary-button>
                <x-primary-button wire:loading.attr="disabled">
                    Send Inquiry
                </x-primary-button>
            </div>
        </form>
    </x-modal>
</div>
