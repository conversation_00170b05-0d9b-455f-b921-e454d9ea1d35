<x-app-layout>
    <!-- Home Page -->
    <div x-show="currentPage === 'home'" x-cloak class="fade-in">
        <!-- Hero Section -->
        <section class="gradient-bg text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                <div class="text-center">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6">
                        Find Your Perfect
                        <span class="block">Dream Home</span>
                    </h1>
                    <p class="text-xl md:text-2xl mb-12 text-blue-100">
                        Discover amazing properties with our modern, intuitive platform
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="max-w-4xl mx-auto bg-white p-6 rounded-2xl shadow-2xl">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="md:col-span-2">
                                <input 
                                    type="text" 
                                    placeholder="Search by location, property type..."
                                    x-model="searchQuery"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                                >
                            </div>
                            <select x-model="propertyType" class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900">
                                <option value="all">All Types</option>
                                <option value="apartment">Apartment</option>
                                <option value="house">House</option>
                                <option value="land">Land</option>
                                <option value="single-room">Single Room</option>
                            </select>
                            <button @click="currentPage = 'search'" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                <i class="fas fa-search mr-2"></i>
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Properties -->
        <section class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Properties</h2>
                    <p class="text-xl text-gray-600">Discover our handpicked selection of premium properties</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <template x-for="property in properties" :key="property.id">
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-hover cursor-pointer" @click="selectedProperty = property; currentPage = 'property'">
                            <div class="relative">
                                <img :src="property.image" :alt="property.title" class="w-full h-64 object-cover">
                                <div class="absolute top-4 left-4">
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium" x-text="property.listingType"></span>
                                </div>
                                <div class="absolute top-4 right-4">
                                    <button class="bg-white/80 hover:bg-white text-gray-700 p-2 rounded-full transition-colors">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-2" x-text="property.title"></h3>
                                <p class="text-gray-600 mb-3" x-text="property.location"></p>
                                <div class="flex items-center justify-between mb-4">
                                    <div class="text-2xl font-bold text-blue-600">
                                        $<span x-text="property.price.toLocaleString()"></span>
                                        <span class="text-sm text-gray-500" x-text="property.listingType === 'For Rent' ? '/month' : ''"></span>
                                    </div>
                                </div>
                                <div class="flex items-center text-sm text-gray-600 space-x-4">
                                    <div class="flex items-center" x-show="property.bedrooms > 0">
                                        <i class="fas fa-bed mr-1"></i>
                                        <span x-text="property.bedrooms"></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-bath mr-1"></i>
                                        <span x-text="property.bathrooms"></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-ruler-combined mr-1"></i>
                                        <span x-text="property.sqft"></span> sqft
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-16 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Ready to List Your Property?</h2>
                <p class="text-xl text-gray-600 mb-8">Join thousands of property owners and agents using Lokus</p>
                <button @click="currentPage = 'create-listing'" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-colors">
                    Start Listing Today
                </button>
            </div>
        </section>
    </div>
</x-app-layout>
