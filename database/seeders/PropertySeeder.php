<?php

namespace Database\Seeders;

use App\Models\Property;
use App\Models\User;
use Illuminate\Database\Seeder;

class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $listers = User::where('role', 'lister')->get();
        
        if ($listers->isEmpty()) {
            $this->command->warn('No lister users found. Please run the main seeder first.');
            return;
        }

        // Sample property data with realistic information
        $properties = [
            [
                'property_type' => 'apartment',
                'listing_type' => 'for_rent',
                'title' => 'Modern Downtown Apartment',
                'description' => 'Beautiful 2-bedroom apartment in the heart of downtown. Features include hardwood floors, stainless steel appliances, and a private balcony with city views. Walking distance to restaurants, shopping, and public transportation.',
                'price' => 2500,
                'currency' => 'USD',
                'address_line_1' => '123 Main Street, Apt 4B',
                'city' => 'New York',
                'state_region' => 'NY',
                'zip_code' => '10001',
                'latitude' => 40.7589,
                'longitude' => -73.9851,
                'features' => [
                    'bedrooms' => 2,
                    'bathrooms' => 2,
                    'square_footage' => 1200,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'house',
                'listing_type' => 'for_sale',
                'title' => 'Charming Victorian Home',
                'description' => 'Stunning 4-bedroom Victorian home with original architectural details. Recently renovated kitchen and bathrooms. Large backyard perfect for entertaining. Located in a quiet, family-friendly neighborhood.',
                'price' => 750000,
                'currency' => 'USD',
                'address_line_1' => '456 Oak Avenue',
                'city' => 'San Francisco',
                'state_region' => 'CA',
                'zip_code' => '94102',
                'latitude' => 37.7749,
                'longitude' => -122.4194,
                'features' => [
                    'bedrooms' => 4,
                    'bathrooms' => 3,
                    'square_footage' => 2800,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'land',
                'listing_type' => 'for_sale',
                'title' => 'Prime Development Land',
                'description' => 'Excellent opportunity for development! 5-acre plot in rapidly growing area. Zoned for residential development. Utilities available at street. Perfect for subdivision or custom home.',
                'price' => 250000,
                'currency' => 'USD',
                'address_line_1' => '789 Country Road',
                'city' => 'Austin',
                'state_region' => 'TX',
                'zip_code' => '78701',
                'latitude' => 30.2672,
                'longitude' => -97.7431,
                'features' => [
                    'plot_size' => 217800, // 5 acres in sq ft
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'single_room',
                'listing_type' => 'for_rent',
                'title' => 'Cozy Studio Near University',
                'description' => 'Perfect for students or young professionals. Furnished studio apartment with kitchenette and private bathroom. All utilities included. Close to campus and public transportation.',
                'price' => 800,
                'currency' => 'USD',
                'address_line_1' => '321 College Street',
                'city' => 'Boston',
                'state_region' => 'MA',
                'zip_code' => '02115',
                'latitude' => 42.3601,
                'longitude' => -71.0589,
                'features' => [
                    'bedrooms' => 0,
                    'bathrooms' => 1,
                    'square_footage' => 450,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'apartment',
                'listing_type' => 'for_sale',
                'title' => 'Luxury Penthouse Suite',
                'description' => 'Exclusive penthouse with panoramic city views. Features include marble countertops, floor-to-ceiling windows, private elevator access, and rooftop terrace. Building amenities include concierge, gym, and pool.',
                'price' => 1200000,
                'currency' => 'USD',
                'address_line_1' => '555 Skyline Drive, PH1',
                'city' => 'Miami',
                'state_region' => 'FL',
                'zip_code' => '33131',
                'latitude' => 25.7617,
                'longitude' => -80.1918,
                'features' => [
                    'bedrooms' => 3,
                    'bathrooms' => 3,
                    'square_footage' => 2200,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'house',
                'listing_type' => 'for_rent',
                'title' => 'Family Home with Pool',
                'description' => 'Spacious family home in quiet suburban neighborhood. Features include updated kitchen, hardwood floors throughout, and large backyard with swimming pool. Great schools nearby.',
                'price' => 3200,
                'currency' => 'USD',
                'address_line_1' => '888 Maple Lane',
                'city' => 'Phoenix',
                'state_region' => 'AZ',
                'zip_code' => '85001',
                'latitude' => 33.4484,
                'longitude' => -112.0740,
                'features' => [
                    'bedrooms' => 4,
                    'bathrooms' => 3,
                    'square_footage' => 2600,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'apartment',
                'listing_type' => 'for_rent',
                'title' => 'Industrial Loft Conversion',
                'description' => 'Unique loft space in converted warehouse. High ceilings, exposed brick walls, and large windows. Open floor plan perfect for creative professionals. Pet-friendly building.',
                'price' => 2800,
                'currency' => 'USD',
                'address_line_1' => '777 Industrial Way, Unit 12',
                'city' => 'Portland',
                'state_region' => 'OR',
                'zip_code' => '97201',
                'latitude' => 45.5152,
                'longitude' => -122.6784,
                'features' => [
                    'bedrooms' => 1,
                    'bathrooms' => 1,
                    'square_footage' => 1800,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'house',
                'listing_type' => 'for_sale',
                'title' => 'Waterfront Cottage',
                'description' => 'Charming lakefront cottage with private dock. Perfect weekend getaway or year-round residence. Recently updated with modern amenities while maintaining rustic charm.',
                'price' => 450000,
                'currency' => 'USD',
                'address_line_1' => '999 Lakeshore Drive',
                'city' => 'Lake Tahoe',
                'state_region' => 'CA',
                'zip_code' => '96150',
                'latitude' => 39.0968,
                'longitude' => -120.0324,
                'features' => [
                    'bedrooms' => 3,
                    'bathrooms' => 2,
                    'square_footage' => 1600,
                ],
                'status' => 'published',
            ],
        ];

        // Additional properties with different statuses
        $additionalProperties = [
            [
                'property_type' => 'apartment',
                'listing_type' => 'for_rent',
                'title' => 'Luxury High-Rise Apartment',
                'description' => 'Brand new luxury apartment with floor-to-ceiling windows and city views. Building amenities include fitness center, rooftop pool, and 24/7 concierge.',
                'price' => 3500,
                'currency' => 'USD',
                'address_line_1' => '1111 Tower Boulevard, Unit 2501',
                'city' => 'Chicago',
                'state_region' => 'IL',
                'zip_code' => '60601',
                'latitude' => 41.8781,
                'longitude' => -87.6298,
                'features' => [
                    'bedrooms' => 2,
                    'bathrooms' => 2,
                    'square_footage' => 1400,
                ],
                'status' => 'draft',
            ],
            [
                'property_type' => 'house',
                'listing_type' => 'for_sale',
                'title' => 'Historic Colonial Home',
                'description' => 'Beautiful colonial home with original hardwood floors and period details. Updated kitchen and bathrooms. Large lot with mature trees.',
                'price' => 650000,
                'currency' => 'USD',
                'address_line_1' => '222 Heritage Lane',
                'city' => 'Philadelphia',
                'state_region' => 'PA',
                'zip_code' => '19101',
                'latitude' => 39.9526,
                'longitude' => -75.1652,
                'features' => [
                    'bedrooms' => 4,
                    'bathrooms' => 2,
                    'square_footage' => 2400,
                ],
                'status' => 'sold',
            ],
            [
                'property_type' => 'single_room',
                'listing_type' => 'for_rent',
                'title' => 'Shared House Room',
                'description' => 'Private room in shared house with common kitchen and living areas. Great for young professionals. Includes utilities and WiFi.',
                'price' => 600,
                'currency' => 'USD',
                'address_line_1' => '333 Shared Street',
                'city' => 'Seattle',
                'state_region' => 'WA',
                'zip_code' => '98101',
                'latitude' => 47.6062,
                'longitude' => -122.3321,
                'features' => [
                    'bedrooms' => 1,
                    'bathrooms' => 1,
                    'square_footage' => 200,
                ],
                'status' => 'rented',
            ],
            [
                'property_type' => 'land',
                'listing_type' => 'for_sale',
                'title' => 'Mountain View Acreage',
                'description' => 'Beautiful 10-acre parcel with stunning mountain views. Perfect for building your dream home. Power and water available.',
                'price' => 180000,
                'currency' => 'USD',
                'address_line_1' => '444 Mountain View Road',
                'city' => 'Denver',
                'state_region' => 'CO',
                'zip_code' => '80201',
                'latitude' => 39.7392,
                'longitude' => -104.9903,
                'features' => [
                    'plot_size' => 435600, // 10 acres in sq ft
                ],
                'status' => 'under_offer',
            ],
            [
                'property_type' => 'apartment',
                'listing_type' => 'for_sale',
                'title' => 'Beachfront Condo',
                'description' => 'Stunning oceanfront condominium with direct beach access. Recently renovated with high-end finishes. Resort-style amenities.',
                'price' => 850000,
                'currency' => 'USD',
                'address_line_1' => '555 Ocean Drive, Unit 801',
                'city' => 'San Diego',
                'state_region' => 'CA',
                'zip_code' => '92101',
                'latitude' => 32.7157,
                'longitude' => -117.1611,
                'features' => [
                    'bedrooms' => 2,
                    'bathrooms' => 2,
                    'square_footage' => 1100,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'house',
                'listing_type' => 'for_rent',
                'title' => 'Modern Townhouse',
                'description' => 'Contemporary 3-story townhouse in gated community. Features include granite countertops, stainless appliances, and attached garage.',
                'price' => 2200,
                'currency' => 'USD',
                'address_line_1' => '666 Modern Way',
                'city' => 'Las Vegas',
                'state_region' => 'NV',
                'zip_code' => '89101',
                'latitude' => 36.1699,
                'longitude' => -115.1398,
                'features' => [
                    'bedrooms' => 3,
                    'bathrooms' => 3,
                    'square_footage' => 1800,
                ],
                'status' => 'published',
            ],
            [
                'property_type' => 'single_room',
                'listing_type' => 'for_rent',
                'title' => 'Executive Studio',
                'description' => 'Fully furnished executive studio in business district. Perfect for traveling professionals. All utilities and services included.',
                'price' => 1200,
                'currency' => 'USD',
                'address_line_1' => '777 Business Plaza, Studio 15',
                'city' => 'Atlanta',
                'state_region' => 'GA',
                'zip_code' => '30301',
                'latitude' => 33.7490,
                'longitude' => -84.3880,
                'features' => [
                    'bedrooms' => 0,
                    'bathrooms' => 1,
                    'square_footage' => 500,
                ],
                'status' => 'draft',
            ],
        ];

        // Combine all properties
        $allProperties = array_merge($properties, $additionalProperties);

        // Available images in storage (store relative paths only)
        $availableImages = [
            'properties/683bedfeebcd8.jpg',
            'properties/683bedff25721.jpg',
            'properties/bnJgyCjVTViQrnrwiipse20adTTxCkL2mhG4m8UH.jpg',
            'properties/modern-apartment.jpg',
            'properties/luxury-home.jpg',
            'properties/cozy-property.jpg'
        ];

        // Create properties and assign to random listers with images
        foreach ($allProperties as $propertyData) {
            $lister = $listers->random();

            // Assign 1-3 random images to each property (80% chance of having images)
            $images = [];
            if (rand(1, 100) <= 80) { // 80% chance of having images
                $numImages = rand(1, 3);
                $selectedImages = array_rand($availableImages, min($numImages, count($availableImages)));

                if (is_array($selectedImages)) {
                    foreach ($selectedImages as $imageIndex) {
                        $images[] = $availableImages[$imageIndex];
                    }
                } else {
                    $images[] = $availableImages[$selectedImages];
                }

                // Remove duplicates
                $images = array_unique($images);
            }

            Property::create(array_merge($propertyData, [
                'user_id' => $lister->id,
                'images' => $images,
            ]));
        }

        $this->command->info('Created ' . count($allProperties) . ' properties with various statuses, types, and images.');
    }
}
