<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create test users with known credentials
        $admin = User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '******-0101',
            'is_active' => true,
        ]);

        $lister1 = User::factory()->create([
            'name' => 'John Lister',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'lister',
            'phone' => '******-0102',
            'is_active' => true,
        ]);

        $lister2 = User::factory()->create([
            'name' => 'Sarah Property Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'lister',
            'phone' => '******-0103',
            'is_active' => true,
        ]);

        $seeker = User::factory()->create([
            'name' => 'Jane Seeker',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'seeker',
            'phone' => '******-0104',
            'is_active' => true,
        ]);

        // Create additional users
        User::factory(5)->create([
            'role' => 'lister',
            'is_active' => true,
        ]);

        User::factory(8)->create([
            'role' => 'seeker',
            'is_active' => true,
        ]);

        // Call property seeder
        $this->call(PropertySeeder::class);
    }
}
