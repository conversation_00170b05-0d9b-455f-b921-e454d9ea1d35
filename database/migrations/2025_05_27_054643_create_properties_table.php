<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('property_type');
            $table->string('listing_type'); // For Sale, For Rent
            $table->string('title');
            $table->text('description');
            $table->decimal('price', 15, 2);
            $table->string('currency')->default('USD');
            $table->string('address_line_1');
            $table->string('city');
            $table->string('state_region')->nullable();
            $table->string('zip_code')->nullable();
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->json('features')->nullable(); // For dynamic features like bedrooms, bathrooms, etc.
            $table->json('images')->nullable(); // Store paths to images
            $table->string('status')->default('draft'); // draft, published, sold, rented, under_offer
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties');
    }
};
