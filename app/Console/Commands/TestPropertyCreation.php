<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Property;
use Illuminate\Http\Request; // Keep for Request::create if needed elsewhere, but not for direct model creation
use Illuminate\Support\Facades\Auth; // Keep for Auth::login/logout if needed for context
use App\Http\Controllers\PropertyController; // Keep if needed for other methods, but not for store
use Illuminate\Support\Facades\Hash;

class TestPropertyCreation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-property-creation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tests the property creation process for a lister user by directly creating a model.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting property creation test (direct model creation)...');

        // 1. Create a temporary lister user
        $this->info('Creating a temporary lister user...');
        $lister = User::create([
            'name' => 'Test Lister',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'lister',
        ]);

        if (!$lister) {
            $this->error('Failed to create lister user.');
            return Command::FAILURE;
        }
        $this->info('Lister user created: ' . $lister->email);

        // 2. Directly create a Property model instance
        $this->info('Attempting to directly create a Property model instance...');
        try {
            $property = Property::create([
                'user_id' => $lister->id,
                'property_type' => 'apartment',
                'listing_type' => 'for_sale',
                'title' => 'Directly Created Test Property',
                'description' => 'This property was created directly via the Artisan command for testing.',
                'price' => 99999,
                'currency' => 'USD',
                'address_line_1' => '456 Direct Test Ave',
                'city' => 'Model City',
                'state_region' => 'MD',
                'zip_code' => '98765',
                'bedrooms' => 1,
                'bathrooms' => 1,
                'square_footage' => 500,
                'plot_size' => null, // Not applicable for apartment
                'latitude' => 35.000000,
                'longitude' => -120.000000,
                'images' => [],
                'features' => [],
                'status' => 'published',
            ]);

            if (!$property) {
                $this->error('Failed to create property model.');
                // Clean up user
                $lister->delete();
                return Command::FAILURE;
            }
            $this->info('Property model created successfully. ID: ' . $property->id);

            // 3. Verify if the property was successfully created in the database
            $this->info('Verifying property existence in database...');
            $foundProperty = Property::find($property->id);

            if ($foundProperty) {
                $this->info('Property "' . $foundProperty->title . '" found in database. Property creation test PASSED!');
                $this->info('Property details:');
                $this->info('  User ID: ' . $foundProperty->user_id);
                $this->info('  Type: ' . $foundProperty->property_type);
                $this->info('  Price: ' . $foundProperty->price);
            } else {
                $this->error('Property NOT found in database after creation. Property creation test FAILED!');
                // Clean up user
                $lister->delete();
                return Command::FAILURE;
            }

            // 4. Clean up the created user and property
            $this->info('Cleaning up temporary user and property...');
            $foundProperty->delete();
            $lister->delete();
            $this->info('Cleanup complete.');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Error during direct property creation: ' . $e->getMessage());
            // Clean up user if created
            if (isset($lister) && $lister->exists) {
                $lister->delete();
            }
            return Command::FAILURE;
        }
    }
}
