<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PropertyResource\Pages;
use App\Filament\Resources\PropertyResource\RelationManagers;
use App\Models\Property;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;

class PropertyResource extends Resource
{
    protected static ?string $model = Property::class;

    protected static ?string $navigationIcon = 'heroicon-o-home';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Textarea::make('description')
                    ->required()
                    ->maxLength(65535)
                    ->columnSpanFull(),
                TextInput::make('price')
                    ->numeric()
                    ->required()
                    ->prefix('$'),
                Select::make('property_type')
                    ->options([
                        'apartment' => 'Apartment',
                        'house' => 'House/Bungalow',
                        'land' => 'Land',
                        'single_room' => 'Single Room',
                        'hotel_room' => 'Hotel Room',
                        'office_space' => 'Office Space',
                    ])
                    ->required(),
                Select::make('listing_type')
                    ->options([
                        'for_sale' => 'For Sale',
                        'for_rent' => 'For Rent',
                    ])
                    ->required(),
                TextInput::make('address')
                    ->maxLength(255),
                TextInput::make('city')
                    ->maxLength(255),
                TextInput::make('state_region')
                    ->label('State/Region')
                    ->maxLength(255),
                TextInput::make('zip_code')
                    ->maxLength(255),
                TextInput::make('latitude')
                    ->numeric()
                    ->nullable()
                    ->label('Latitude')
                    // Add step attribute for decimal input if Filament supports it directly
                    // or use rule 'regex:/^\d{1,3}\.\d{1,7}$/' for specific format
                    ->helperText('Enter latitude, e.g., 34.052235'),
                TextInput::make('longitude')
                    ->numeric()
                    ->nullable()
                    ->label('Longitude')
                    ->helperText('Enter longitude, e.g., -118.243683'),
                TextInput::make('bedrooms')
                    ->numeric()
                    ->nullable(),
                TextInput::make('bathrooms')
                    ->numeric()
                    ->nullable(),
                TextInput::make('square_footage')
                    ->numeric()
                    ->nullable(),
                TextInput::make('plot_size')
                    ->numeric()
                    ->nullable(),
                SpatieMediaLibraryFileUpload::make('images')
                    ->collection('property_images') // Define a media collection name
                    ->multiple()
                    ->image()
                    ->reorderable()
                    ->maxFiles(15)
                    ->visibility('public'),
                Select::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'published' => 'Published',
                        'pending_approval' => 'Pending Approval',
                        'sold' => 'Sold',
                        'rented' => 'Rented',
                    ])
                    ->required()
                    ->default('pending_approval'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('images')
                    ->collection('property_images')
                    ->conversion('thumb') // Optional: define a conversion in your Media model
                    ->circular(),
                TextColumn::make('title')
                    ->searchable(),
                TextColumn::make('property_type')
                    ->sortable(),
                TextColumn::make('listing_type')
                    ->sortable(),
                TextColumn::make('price')
                    ->money('usd')
                    ->sortable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'draft' => 'gray',
                        'published' => 'success',
                        'pending_approval' => 'warning',
                        'sold' => 'danger',
                        'rented' => 'danger',
                    })
                    ->sortable(),
                TextColumn::make('user.name')
                    ->label('Lister')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Action::make('approve')
                    ->action(function (Property $record) {
                        $record->status = 'published';
                        $record->save();
                    })
                    ->label('Approve')
                    ->color('success')
                    ->icon('heroicon-o-check-circle')
                    ->hidden(fn (Property $record): bool => $record->status === 'published'),
                Action::make('reject')
                    ->action(function (Property $record) {
                        $record->status = 'draft'; // Or a specific 'rejected' status
                        $record->save();
                    })
                    ->label('Reject')
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->hidden(fn (Property $record): bool => $record->status === 'draft' || $record->status === 'published'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProperties::route('/'),
            'create' => Pages\CreateProperty::route('/create'),
            'edit' => Pages\EditProperty::route('/{record}/edit'),
        ];
    }
}
