<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Property; // Added for Lokus MVP

class PropertyInquiry extends Mailable
{
    use Queueable, SerializesModels;

    public $property;
    public $inquirerName;
    public $inquirerEmail;
    public $inquirerPhone; // Added for Lokus MVP
    public $inquiryMessage;

    /**
     * Create a new message instance.
     */
    public function __construct(Property $property, string $inquirerName, string $inquirerEmail, ?string $inquirerPhone, string $inquiryMessage)
    {
        $this->property = $property;
        $this->inquirerName = $inquirerName;
        $this->inquirerEmail = $inquirerEmail;
        $this->inquirerPhone = $inquirerPhone;
        $this->inquiryMessage = $inquiryMessage;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New Inquiry for Your Property: ' . $this->property->title,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.property-inquiry',
            with: [
                'listerName' => $this->property->user->name,
                'propertyTitle' => $this->property->title,
                'propertyId' => $this->property->id,
                'inquirerName' => $this->inquirerName,
                'inquirerEmail' => $this->inquirerEmail,
                'inquiryMessage' => $this->inquiryMessage,
                'propertyUrl' => route('properties.show', $this->property->id),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
