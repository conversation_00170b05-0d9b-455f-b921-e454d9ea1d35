<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Property extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $fillable = [
        'user_id',
        'property_type',
        'listing_type',
        'title',
        'description',
        'price',
        'currency',
        'address_line_1',
        'city',
        'state_region',
        'zip_code',
        'latitude',
        'longitude',
        'features',
        'images', // Re-enabled for JSON array storage
        'status',
    ];

    protected $casts = [
        'features' => 'array',
        'images' => 'array', // Re-enabled for JSON array storage
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
