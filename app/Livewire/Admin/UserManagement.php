<?php

namespace App\Livewire\Admin;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class UserManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $roleFilter = '';
    public $statusFilter = '';
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    protected $queryString = [
        'search' => ['except' => ''],
        'roleFilter' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updating($key)
    {
        if (in_array($key, ['search', 'roleFilter', 'statusFilter', 'sortField', 'sortDirection'])) {
            $this->resetPage();
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $users = User::query();

        if ($this->search) {
            $users->where(function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('phone', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->roleFilter) {
            $users->where('role', $this->roleFilter);
        }

        if ($this->statusFilter !== '') {
            $users->where('is_active', (bool)$this->statusFilter);
        }

        $users->orderBy($this->sortField, $this->sortDirection);

        return view('livewire.admin.user-management', [
            'users' => $users->paginate(10),
        ]);
    }

    public function toggleActive($userId)
    {
        $user = User::findOrFail($userId);
        $user->is_active = !$user->is_active;
        $user->save();
    }

    public function updateRole($userId, $role)
    {
        $user = User::findOrFail($userId);
        $user->role = $role;
        $user->save();
    }

    public function deleteUser($userId)
    {
        User::destroy($userId);
    }
}
