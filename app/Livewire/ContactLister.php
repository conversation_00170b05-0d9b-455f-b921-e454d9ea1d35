<?php

namespace App\Livewire;

use App\Mail\PropertyInquiry;
use App\Models\Property;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class ContactLister extends Component
{
    public $property; // Will be an instance of Property
    public $name = '';
    public $email = '';
    public $phone = '';
    public $message = '';
    public $showModal = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'message' => 'required|string|min:10',
    ];

    public function mount($propertyId)
    {
        $this->property = Property::with('user')->findOrFail($propertyId);
        if (Auth::check()) {
            $this->name = Auth::user()->name;
            $this->email = Auth::user()->email;
            $this->phone = Auth::user()->phone;
        }
    }

    public function sendInquiry()
    {
        $this->validate();

        Mail::to($this->property->user->email)->send(new PropertyInquiry(
            $this->property,
            $this->name,
            $this->email,
            $this->phone,
            $this->message
        ));

        session()->flash('inquiry_message', 'Your inquiry has been sent successfully!');
        $this->reset(['name', 'email', 'phone', 'message']); // Clear form fields
        $this->showModal = false; // Close the modal
    }

    public function render()
    {
        return view('livewire.contact-lister');
    }
}
