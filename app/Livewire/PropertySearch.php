<?php

namespace App\Livewire;

use App\Models\Property;
use Livewire\Component;
use Livewire\WithPagination;

class PropertySearch extends Component
{
    use WithPagination;

    public $keywords = '';
    public $property_type = '';
    public $listing_type = '';
    public $min_price = '';
    public $max_price = '';
    public $sort_by = 'created_at';
    public $sort_direction = 'desc';
    public $layout_view = 'grid'; // 'grid' or 'list'
    public $total_properties;

    protected $queryString = [
        'keywords' => ['except' => ''],
        'property_type' => ['except' => ''],
        'listing_type' => ['except' => ''],
        'min_price' => ['except' => ''],
        'max_price' => ['except' => ''],
        'sort_by' => ['except' => 'created_at'],
        'sort_direction' => ['except' => 'desc'],
        'layout_view' => ['except' => 'grid'],
    ];

    public function mount()
    {
        $this->updateTotalProperties();
    }

    public function updating($key)
    {
        if (in_array($key, ['keywords', 'property_type', 'listing_type', 'min_price', 'max_price', 'sort_by', 'sort_direction'])) {
            $this->resetPage();
        }
    }

    public function render()
    {
        $query = Property::query();

        // Apply keywords search
        if ($this->keywords) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->keywords . '%')
                  ->orWhere('description', 'like', '%' . $this->keywords . '%')
                  ->orWhere('city', 'like', '%' . $this->keywords . '%')
                  ->orWhere('state_region', 'like', '%' . $this->keywords . '%');
            });
        }

        // Apply filters
        if ($this->property_type) {
            $query->where('property_type', $this->property_type);
        }

        if ($this->listing_type) {
            $query->where('listing_type', $this->listing_type);
        }

        if ($this->min_price) {
            $query->where('price', '>=', $this->min_price);
        }

        if ($this->max_price) {
            $query->where('price', '<=', $this->max_price);
        }

        // Only show published properties
        $query->where('status', 'published');

        // Apply sorting
        $query->orderBy($this->sort_by, $this->sort_direction);

        $properties = $query->paginate(10);
        $this->total_properties = $properties->total();

        return view('livewire.property-search', [
            'properties' => $properties,
        ]);
    }

    public function updateTotalProperties()
    {
        $query = Property::query();

        // Apply keywords search
        if ($this->keywords) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->keywords . '%')
                  ->orWhere('description', 'like', '%' . $this->keywords . '%')
                  ->orWhere('city', 'like', '%' . $this->keywords . '%')
                  ->orWhere('state_region', 'like', '%' . $this->keywords . '%');
            });
        }

        // Apply filters
        if ($this->property_type) {
            $query->where('property_type', $this->property_type);
        }

        if ($this->listing_type) {
            $query->where('listing_type', $this->listing_type);
        }

        if ($this->min_price) {
            $query->where('price', '>=', $this->min_price);
        }

        if ($this->max_price) {
            $query->where('price', '<=', $this->max_price);
        }

        // Only show published properties
        $query->where('status', 'published');

        $this->total_properties = $query->count();
    }

    public function showProperty($propertyId)
    {
        return redirect()->route('properties.show', $propertyId);
    }
}
