graph TD
    A[User Browser] -->|HTTP Request| B(Laravel Web Server);

    subgraph "Laravel Application Core"
        B --> C{Routes (`routes/web.php`)};

        C -->|Standard Web Routes| D[Controllers <br/> e.g., PropertyController, ProfileController, AdminController];
        D --> E[Eloquent Models <br/> e.g., Property, User];
        E --> F[Database];
        D --> G[Blade Views <br/> (`resources/views/*`)];
        G --> A;
        D --> H[Mail Services <br/> e.g., PropertyInquiry Mailer];
        H --> I[External Email Service];


        C -->|Livewire AJAX Requests / Initial Load| J[Livewire Components <br/> e.g., PropertySearch, CreateProperty, Admin/UserManagement];
        J --> E;
        J --> K[Livewire Blade Views <br/> (`resources/views/livewire/*`)];
        K -.->|Dynamic Update| A;

        C -->|Admin Panel Routes (`/filament`)| L[Filament Admin Panel Engine];
        L --> M[Filament Resources <br/> e.g., PropertyResource, UserResource];
        M -- Manages --> E;
        M --> N[Filament UI <br/> (Forms, Tables, Actions)];
        N --> A;
    end

    style A fill:#lightgrey,stroke:#333,stroke-width:2px
    style B fill:#lightblue,stroke:#333,stroke-width:2px
    style F fill:#lightyellow,stroke:#333,stroke-width:2px
    style I fill:#lightpink,stroke:#333,stroke-width:2px
    style G fill:#honeydew,stroke:#333,stroke-width:1px
    style K fill:#honeydew,stroke:#333,stroke-width:1px
    style N fill:#lavender,stroke:#333,stroke-width:1px
```

This diagram illustrates:
-   **User Browser**: Initiates requests.
-   **Laravel Web Server**: Handles incoming requests.
-   **Routes (`routes/web.php`)**: Directs requests to appropriate handlers.
-   **Controllers**: Handle business logic for standard web requests, interact with Models, and return Blade Views or Mailables.
-   **Livewire Components**: Manage dynamic UI sections, interact with Models, and render their own Blade Views, updating the browser dynamically.
-   **Filament Admin Panel**: Provides a pre-built admin interface, with Resources defining how Models are managed (forms, tables, actions).
-   **Eloquent Models**: Represent the application's data and database interaction logic.
-   **Database**: Stores application data.
-   **Blade Views**: Render HTML sent to the browser.
-   **Mail Services**: Handle outgoing emails.
